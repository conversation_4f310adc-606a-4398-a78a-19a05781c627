#ifndef RNN_DATA_H
#define RNN_DATA_H

#include "opus_types.h"

/* 定义神经网络层的数据类型 */
typedef opus_int8 rnn_weight;

/* Dense层结构定义 */
typedef struct {
   const rnn_weight *bias;
   const rnn_weight *weights;
   int nb_inputs;
   int nb_outputs;
   int activation;
} DenseLayer;

/* GRU层结构定义 */
typedef struct {
   const rnn_weight *bias;
   const rnn_weight *weights;
   const rnn_weight *recurrent_weights;
   int nb_inputs;
   int nb_outputs;
   int activation;
} GRULayer;

#include "rnn.h"

struct RNNModel {
  int input_dense_size;
  const DenseLayer *input_dense;

  int vad_gru_size;
  const GRULayer *vad_gru;

  int noise_gru_size;
  const GRULayer *noise_gru;

  int denoise_gru_size;
  const GRULayer *denoise_gru;

  int denoise_output_size;
  const DenseLayer *denoise_output;

  int vad_output_size;
  const DenseLayer *vad_output;
};

struct RNNState {
  const RNNModel *model;
  float *vad_gru_state;
  float *noise_gru_state;
  float *denoise_gru_state;
};


#endif
